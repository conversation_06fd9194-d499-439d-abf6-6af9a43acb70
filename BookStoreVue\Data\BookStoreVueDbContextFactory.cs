﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace BookStoreVue.Data;

public class BookStoreVueDbContextFactory : IDesignTimeDbContextFactory<BookStoreVueDbContext>
{
    public BookStoreVueDbContext CreateDbContext(string[] args)
    {
        BookStoreVueEfCoreEntityExtensionMappings.Configure();
        var configuration = BuildConfiguration();

        var builder = new DbContextOptionsBuilder<BookStoreVueDbContext>()
            .UseSqlServer(configuration.GetConnectionString("Default"));

        return new BookStoreVueDbContext(builder.Options);
    }

    private static IConfigurationRoot BuildConfiguration()
    {
        var builder = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false);

        return builder.Build();
    }
}