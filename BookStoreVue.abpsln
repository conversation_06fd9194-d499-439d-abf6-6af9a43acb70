{
  "id": "2d3747ca-ac66-44ec-a84a-95035bab02d1",
  "template": "app-nolayers",
  "versions": {
    "LeptonX": "4.2.1",
    "AbpFramework": "9.2.1",
    "AbpCommercial": "9.2.1",
    "AbpStudio": "1.0.2",
    "TargetDotnetFramework": "net9.0"
  },
  "modules": {
    "BookStoreVue": {
      "path": "BookStoreVue.abpmdl"
    }
  },
  "runProfiles": {
    "Default": {
      "path": "etc/run-profiles/Default.abprun.json"
    }
  },
  "options": {
    "httpRequests": {
      "ignoredUrls": [
      
      ]
    }
  },
  "creatingStudioConfiguration": {
    "template": "app-nolayers",
    "createdAbpStudioVersion": "1.0.2",
    "multiTenancy": "false",
    "runInstallLibs": "true",
    "useLocalReferences": "false",
    "uiFramework": "mvc",
    "databaseProvider": "ef",
    "runDbMigrator": "true",
    "databaseManagementSystem": "sqlserver",
    "createInitialMigration": "true",
    "theme": "leptonx",
    "themeStyle": "dim",
    "themeMenuPlacement": "side",
    "publicWebsite": "",
    "optionalModules": " TextTemplateManagement LanguageManagement AuditLogging OpenIddictAdmin",
    "socialLogin": "false",
    "selectedLanguages": ["English", "العربية", ],
    "defaultLanguage": "English",
    "createCommand": "abp new BookStoreVue -t app-nolayers --ui-framework mvc --mobile  --database-provider ef --database-management-system sqlserver --theme leptonx --no-tests --without-cms-kit --sample-crud-page --dont-run-bundling --no-multi-tenancy --no-social-logins -no-saas -no-gdpr -no-file-management"
  }
}