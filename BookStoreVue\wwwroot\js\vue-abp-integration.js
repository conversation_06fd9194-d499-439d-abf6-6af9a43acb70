/**
 * Vue.js integration utilities for ABP Framework
 * Provides bridge between Vue components and ABP services
 */

// Global mixin array for page-specific mixins
window.mixinArray = [];

// Vue-ABP Integration utilities
window.VueAbpIntegration = {
    
    /**
     * ABP Service wrapper for Vue components
     */
    services: {
        // Localization service wrapper
        localization: {
            getResource: function(resourceName) {
                return abp.localization.getResource(resourceName);
            },
            localize: function(key, resourceName) {
                const l = this.getResource(resourceName || 'BookStoreVue');
                return l(key);
            }
        },
        
        // Authorization service wrapper
        auth: {
            isGranted: function(permission) {
                return abp.auth.isGranted(permission);
            }
        },
        
        // Notification service wrapper
        notification: {
            success: function(message, title) {
                abp.notify.success(message, title);
            },
            info: function(message, title) {
                abp.notify.info(message, title);
            },
            warn: function(message, title) {
                abp.notify.warn(message, title);
            },
            error: function(message, title) {
                abp.notify.error(message, title);
            }
        },
        
        // HTTP service wrapper using axios
        http: {
            get: function(url, config) {
                return axios.get(url, config);
            },
            post: function(url, data, config) {
                return axios.post(url, data, config);
            },
            put: function(url, data, config) {
                return axios.put(url, data, config);
            },
            delete: function(url, config) {
                return axios.delete(url, config);
            }
        }
    },
    
    /**
     * Modal utilities for Vue components
     */
    modal: {
        /**
         * Open a modal page and return a promise
         * @param {string} url - Modal page URL
         * @param {object} options - Modal options
         */
        open: function(url, options) {
            return new Promise((resolve, reject) => {
                // Create modal container
                const modalId = 'vue-modal-' + Date.now();
                const modalHtml = `
                    <div class="modal fade" id="${modalId}" tabindex="-1" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-body p-0">
                                    <div id="${modalId}-content">Loading...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                document.body.insertAdjacentHTML('beforeend', modalHtml);
                const modalElement = document.getElementById(modalId);
                
                // Load modal content
                axios.get(url, { params: options })
                    .then(response => {
                        document.getElementById(modalId + '-content').innerHTML = response.data;
                        
                        // Initialize Bootstrap modal
                        const modal = new bootstrap.Modal(modalElement);
                        modal.show();
                        
                        // Handle modal close
                        modalElement.addEventListener('hidden.bs.modal', () => {
                            modalElement.remove();
                        });
                        
                        // Handle form submission
                        const form = modalElement.querySelector('form');
                        if (form) {
                            form.addEventListener('submit', (e) => {
                                e.preventDefault();
                                
                                const formData = new FormData(form);
                                axios.post(form.action, formData)
                                    .then(response => {
                                        modal.hide();
                                        resolve(response.data);
                                    })
                                    .catch(error => {
                                        reject(error);
                                    });
                            });
                        }
                    })
                    .catch(error => {
                        modalElement.remove();
                        reject(error);
                    });
            });
        }
    },
    
    /**
     * DataTable utilities for Vue components
     */
    dataTable: {
        /**
         * Create a server-side DataTable configuration for Vue
         * @param {object} options - DataTable options
         */
        createConfig: function(options) {
            return {
                serverSide: true,
                processing: true,
                ajax: {
                    url: options.url,
                    type: 'POST',
                    data: function(d) {
                        // Convert DataTables parameters to ABP format
                        return {
                            skipCount: d.start,
                            maxResultCount: d.length,
                            sorting: d.columns[d.order[0].column].data + ' ' + d.order[0].dir,
                            ...options.extraParams
                        };
                    },
                    dataSrc: function(json) {
                        // Convert ABP response to DataTables format
                        return json.items || json.result?.items || [];
                    }
                },
                columns: options.columns,
                pageLength: options.pageLength || 10,
                lengthMenu: options.lengthMenu || [[10, 25, 50, 100], [10, 25, 50, 100]],
                order: options.order || [[0, 'asc']],
                searching: options.searching !== false,
                ...options.extraOptions
            };
        }
    },
    
    /**
     * Utility functions
     */
    utils: {
        /**
         * Format date using ABP's date formatting
         */
        formatDate: function(date, format) {
            if (!date) return '';
            return luxon.DateTime.fromISO(date).toFormat(format || 'dd/MM/yyyy');
        },
        
        /**
         * Format datetime using ABP's datetime formatting
         */
        formatDateTime: function(date, format) {
            if (!date) return '';
            return luxon.DateTime.fromISO(date).toFormat(format || 'dd/MM/yyyy HH:mm');
        },
        
        /**
         * Confirm dialog using SweetAlert2
         */
        confirm: function(message, title) {
            return Swal.fire({
                title: title || 'Confirm',
                text: message,
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'Yes',
                cancelButtonText: 'No'
            }).then(result => result.isConfirmed);
        }
    }
};

/**
 * Global Vue mixin with ABP integration
 */
window.AbpVueMixin = {
    data: function() {
        return {
            loading: false,
            errors: {}
        };
    },
    
    methods: {
        // Localization methods
        l: function(key, resourceName) {
            return VueAbpIntegration.services.localization.localize(key, resourceName);
        },
        
        // Authorization methods
        isGranted: function(permission) {
            return VueAbpIntegration.services.auth.isGranted(permission);
        },
        
        // Notification methods
        notify: VueAbpIntegration.services.notification,
        
        // HTTP methods
        $http: VueAbpIntegration.services.http,
        
        // Modal methods
        openModal: VueAbpIntegration.modal.open,
        
        // Utility methods
        formatDate: VueAbpIntegration.utils.formatDate,
        formatDateTime: VueAbpIntegration.utils.formatDateTime,
        confirm: VueAbpIntegration.utils.confirm,
        
        // Error handling
        handleError: function(error) {
            this.loading = false;
            if (error.response && error.response.data) {
                const errorData = error.response.data;
                if (errorData.error) {
                    this.notify.error(errorData.error.message || 'An error occurred');
                }
                if (errorData.validationErrors) {
                    this.errors = {};
                    errorData.validationErrors.forEach(err => {
                        this.errors[err.name] = err.message;
                    });
                }
            } else {
                this.notify.error('An unexpected error occurred');
            }
        }
    }
};
