using Microsoft.Extensions.Localization;
using BookStoreVue.Localization;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Ui.Branding;

namespace BookStoreVue;

[Dependency(ReplaceServices = true)]
public class BookStoreVueBrandingProvider : DefaultBrandingProvider
{
    private IStringLocalizer<BookStoreVueResource> _localizer;

    public BookStoreVueBrandingProvider(IStringLocalizer<BookStoreVueResource> localizer)
    {
        _localizer = localizer;
    }

    public override string AppName => _localizer["AppName"];
}