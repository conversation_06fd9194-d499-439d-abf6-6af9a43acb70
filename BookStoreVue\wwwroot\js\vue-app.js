/**
 * Main Vue.js application setup for ABP Framework integration
 * This file initializes the Vue app with ABP services and global mixins
 */

// Vue application instance
let vueApp = null;

/**
 * Initialize Vue application
 * This function should be called from the layout after all scripts are loaded
 */
function initializeVueApp() {
    // Ensure Vue is loaded
    if (typeof Vue === 'undefined') {
        console.error('Vue.js is not loaded. Please ensure Vue.js is included before this script.');
        return;
    }
    
    // Configure axios defaults
    if (typeof axios !== 'undefined') {
        // Add CSRF token to all requests
        const token = document.querySelector('input[name="__RequestVerificationToken"]');
        if (token) {
            axios.defaults.headers.common['RequestVerificationToken'] = token.value;
        }
        
        // Add default headers
        axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
        
        // Response interceptor for error handling
        axios.interceptors.response.use(
            response => response,
            error => {
                if (error.response) {
                    switch (error.response.status) {
                        case 401:
                            window.location.href = '/Account/Login';
                            break;
                        case 403:
                            abp.notify.error('You are not authorized to perform this action.');
                            break;
                        case 500:
                            abp.notify.error('An internal server error occurred.');
                            break;
                    }
                }
                return Promise.reject(error);
            }
        );
    }
    
    // Create Vue application
    const { createApp } = Vue;
    
    vueApp = createApp({
        data() {
            return {
                // Global application state
                appName: 'BookStoreVue',
                currentUser: abp.currentUser || {},
                
                // Global loading state
                globalLoading: false,
                
                // Global notification state
                notifications: []
            };
        },
        
        methods: {
            // Global methods available to all components
            
            /**
             * Show global loading indicator
             */
            showLoading() {
                this.globalLoading = true;
            },
            
            /**
             * Hide global loading indicator
             */
            hideLoading() {
                this.globalLoading = false;
            },
            
            /**
             * Refresh current page data
             */
            refreshPage() {
                window.location.reload();
            },
            
            /**
             * Navigate to a URL
             */
            navigateTo(url) {
                window.location.href = url;
            },
            
            /**
             * Get current culture info
             */
            getCurrentCulture() {
                return abp.localization.currentCulture || { name: 'en' };
            },
            
            /**
             * Get application configuration
             */
            getAppConfig() {
                return abp.appPath || '/';
            }
        },
        
        computed: {
            /**
             * Check if user is authenticated
             */
            isAuthenticated() {
                return this.currentUser && this.currentUser.isAuthenticated;
            },
            
            /**
             * Get current user name
             */
            currentUserName() {
                return this.currentUser?.userName || 'Anonymous';
            }
        },
        
        mounted() {
            // Initialize global event listeners
            this.initializeGlobalEvents();
            
            // Emit app ready event
            this.$nextTick(() => {
                window.dispatchEvent(new CustomEvent('vue-app-ready', { detail: this }));
            });
        },
        
        methods: {
            ...this.methods, // Spread existing methods
            
            /**
             * Initialize global event listeners
             */
            initializeGlobalEvents() {
                // Listen for ABP events
                if (typeof abp !== 'undefined' && abp.event) {
                    // Listen for notification events
                    abp.event.on('abp.notifications.received', (notification) => {
                        this.notifications.push(notification);
                    });
                    
                    // Listen for authentication events
                    abp.event.on('abp.auth.signedIn', (user) => {
                        this.currentUser = user;
                    });
                    
                    abp.event.on('abp.auth.signedOut', () => {
                        this.currentUser = {};
                    });
                }
                
                // Listen for page-specific events
                window.addEventListener('vue-component-ready', (event) => {
                    console.log('Vue component ready:', event.detail);
                });
            }
        }
    });
    
    // Add global mixins
    vueApp.mixin(AbpVueMixin);
    
    // Add page-specific mixins
    if (window.mixinArray && window.mixinArray.length > 0) {
        window.mixinArray.forEach(mixin => {
            vueApp.mixin(mixin);
        });
    }
    
    // Mount the Vue application
    vueApp.mount('#vue-app');
    
    // Store reference globally
    window.vueApp = vueApp;
    
    console.log('Vue.js application initialized successfully');
    
    return vueApp;
}

/**
 * Add a mixin to be included in the Vue app
 * This should be called before initializeVueApp()
 */
function addVueMixin(mixin) {
    if (!window.mixinArray) {
        window.mixinArray = [];
    }
    window.mixinArray.push(mixin);
}

/**
 * Get the current Vue app instance
 */
function getVueApp() {
    return window.vueApp;
}

/**
 * Utility function to create a Vue component
 */
function createVueComponent(name, options) {
    if (!vueApp) {
        console.error('Vue app not initialized. Call initializeVueApp() first.');
        return;
    }
    
    return vueApp.component(name, options);
}

// Export functions for global use
window.initializeVueApp = initializeVueApp;
window.addVueMixin = addVueMixin;
window.getVueApp = getVueApp;
window.createVueComponent = createVueComponent;
