@*
    Vue.js Application Container
    This partial view provides the Vue app container and initialization
    Include this in pages that need Vue.js functionality
*@

<div id="vue-app">
    @RenderBody()
</div>

@section scripts {
    <script type="text/javascript">
        // Page-specific Vue initialization
        document.addEventListener('DOMContentLoaded', function() {
            // Ensure Vue app is ready before proceeding
            VueUtils.waitForVueApp().then(function(app) {
                console.log('Vue app is ready for page-specific functionality');
                
                // Emit page ready event
                window.dispatchEvent(new CustomEvent('vue-page-ready', { 
                    detail: { 
                        page: '@ViewContext.RouteData.Values["page"]',
                        controller: '@ViewContext.RouteData.Values["controller"]',
                        action: '@ViewContext.RouteData.Values["action"]'
                    } 
                }));
            });
        });
    </script>
}
