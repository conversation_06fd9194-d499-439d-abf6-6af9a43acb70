/* Vue.js Global Setup and ABP Integration */

// Initialize Vue app when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Create Vue app container by wrapping the main content area
    createVueAppContainer();

    // Wait for ABP to be fully loaded
    if (typeof abp !== 'undefined') {
        initializeVueApp();
    } else {
        // Fallback: wait a bit more for ABP to load
        setTimeout(() => {
            if (typeof abp !== 'undefined') {
                initializeVueApp();
            } else {
                console.warn('ABP Framework not detected. Vue app may not function properly.');
                initializeVueApp();
            }
        }, 100);
    }
});

// Create Vue app container by wrapping the main content
function createVueAppContainer() {
    // Look for common content selectors in LeptonX theme
    const contentSelectors = [
        '.lpx-content-wrapper',
        '.content-wrapper',
        'main',
        '.main-content',
        '#main-content',
        'body > .container-fluid',
        'body > .container'
    ];

    let contentElement = null;

    // Find the main content element
    for (const selector of contentSelectors) {
        contentElement = document.querySelector(selector);
        if (contentElement) {
            break;
        }
    }

    // Fallback: use body if no content wrapper found
    if (!contentElement) {
        contentElement = document.body;
    }

    // Check if Vue app container already exists
    if (document.getElementById('vue-app')) {
        return;
    }

    // Create Vue app wrapper
    const vueAppDiv = document.createElement('div');
    vueAppDiv.id = 'vue-app';

    // Move all content into Vue app container
    while (contentElement.firstChild) {
        vueAppDiv.appendChild(contentElement.firstChild);
    }

    // Add Vue app container to content element
    contentElement.appendChild(vueAppDiv);

    console.log('Vue app container created and content wrapped');
}

// Global utility functions
window.VueUtils = {
    /**
     * Create a simple Vue component for a specific element
     */
    createSimpleComponent: function(elementId, options) {
        const element = document.getElementById(elementId);
        if (!element) {
            console.warn(`Element with ID '${elementId}' not found`);
            return null;
        }

        const { createApp } = Vue;
        const app = createApp(options);
        app.mixin(AbpVueMixin);
        return app.mount(`#${elementId}`);
    },

    /**
     * Wait for Vue app to be ready
     */
    waitForVueApp: function() {
        return new Promise((resolve) => {
            if (window.vueApp) {
                resolve(window.vueApp);
            } else {
                window.addEventListener('vue-app-ready', (event) => {
                    resolve(event.detail);
                });
            }
        });
    }
};
