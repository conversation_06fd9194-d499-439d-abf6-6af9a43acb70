/* Your Global Scripts */

// Temporary placeholder for Vue.js integration
console.log('Global scripts loaded');

// Global utility functions
window.VueUtils = {
    /**
     * Create a simple Vue component for a specific element
     */
    createSimpleComponent: function(elementId, options) {
        const element = document.getElementById(elementId);
        if (!element) {
            console.warn(`Element with ID '${elementId}' not found`);
            return null;
        }

        const { createApp } = Vue;
        const app = createApp(options);
        app.mixin(AbpVueMixin);
        return app.mount(`#${elementId}`);
    },

    /**
     * Wait for Vue app to be ready
     */
    waitForVueApp: function() {
        return new Promise((resolve) => {
            if (window.vueApp) {
                resolve(window.vueApp);
            } else {
                window.addEventListener('vue-app-ready', (event) => {
                    resolve(event.detail);
                });
            }
        });
    }
};
