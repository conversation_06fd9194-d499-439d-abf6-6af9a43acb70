﻿// Vue.js integration for Index page
(function(){
    // Page-specific Vue mixin
    const indexPageMixin = {
        data: function() {
            return {
                welcomeMessage: 'Welcome to BookStore with Vue.js!',
                currentTime: new Date().toLocaleString(),
                isVueWorking: true
            };
        },

        methods: {
            updateTime: function() {
                this.currentTime = new Date().toLocaleString();
            },

            showNotification: function() {
                this.notify.success('Vue.js is working with ABP Framework!');
            }
        },

        mounted: function() {
            // Update time every second
            setInterval(() => {
                this.updateTime();
            }, 1000);

            console.log('Index page Vue mixin mounted');
        }
    };

    // Add the mixin to the global array
    addVueMixin(indexPageMixin);

    // Wait for Vue app to be ready, then add page-specific functionality
    document.addEventListener('vue-app-ready', function() {
        console.log('Vue app ready - Index page specific code can run here');
    });
})();
