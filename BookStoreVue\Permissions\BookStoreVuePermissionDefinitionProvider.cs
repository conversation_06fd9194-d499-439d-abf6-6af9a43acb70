using BookStoreVue.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;
using Volo.Abp.MultiTenancy;

namespace BookStoreVue.Permissions;

public class BookStoreVuePermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        var myGroup = context.AddGroup(BookStoreVuePermissions.GroupName);


        myGroup.AddPermission(BookStoreVuePermissions.Dashboard.Host, L("Permission:Dashboard"), MultiTenancySides.Host);

        var booksPermission = myGroup.AddPermission(BookStoreVuePermissions.Books.Default, L("Permission:Books"));
        booksPermission.AddChild(BookStoreVuePermissions.Books.Create, L("Permission:Books.Create"));
        booksPermission.AddChild(BookStoreVuePermissions.Books.Edit, L("Permission:Books.Edit"));
        booksPermission.AddChild(BookStoreVuePermissions.Books.Delete, L("Permission:Books.Delete"));
        
        //Define your own permissions here. Example:
        //myGroup.AddPermission(BookStoreVuePermissions.MyPermission1, L("Permission:MyPermission1"));
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<BookStoreVueResource>(name);
    }
}
