// Vue.js Books Page Implementation
(function(){
    // Page-specific Vue mixin for Books
    const booksPageMixin = {
        data: function() {
            return {
                // DataTable configuration
                tableColumns: [],
                booksTableRef: null
            };
        },

        methods: {
            // Initialize the books table
            initializeBooksTable: function() {
                const l = abp.localization.getResource('BookStoreVue');

                this.tableColumns = [
                    {
                        title: l('Actions'),
                        sortable: false,
                        rowAction: {
                            items: [
                                {
                                    text: l('Edit'),
                                    visible: () => abp.auth.isGranted('BookStoreVue.Books.Edit'),
                                    action: (data) => this.editBook(data.record.id)
                                },
                                {
                                    text: l('Delete'),
                                    visible: () => abp.auth.isGranted('BookStoreVue.Books.Delete'),
                                    confirmMessage: (data) => l('BookDeletionConfirmationMessage', data.record.name),
                                    action: (data) => this.deleteBook(data.record.id)
                                }
                            ]
                        }
                    },
                    {
                        title: l('Name'),
                        data: "name"
                    },
                    {
                        title: l('Type'),
                        data: "type",
                        render: (data) => l('Enum:BookType.' + data)
                    },
                    {
                        title: l('PublishDate'),
                        data: "publishDate",
                        dataFormat: "datetime"
                    },
                    {
                        title: l('Price'),
                        data: "price"
                    },
                    {
                        title: l('CreationTime'),
                        data: "creationTime",
                        dataFormat: "datetime"
                    }
                ];
            },

            // Create new book
            createBook: function() {
                this.openModal('/Books/CreateModal')
                    .then(() => {
                        this.refreshBooksTable();
                    })
                    .catch(error => {
                        console.error('Error creating book:', error);
                    });
            },

            // Edit existing book
            editBook: function(bookId) {
                this.openModal('/Books/EditModal', { id: bookId })
                    .then(() => {
                        this.refreshBooksTable();
                    })
                    .catch(error => {
                        console.error('Error editing book:', error);
                    });
            },

            // Delete book
            deleteBook: async function(bookId) {
                try {
                    await bookStoreVue.services.books.book.delete(bookId);
                    this.notify.info(this.l('SuccessfullyDeleted'));
                    this.refreshBooksTable();
                } catch (error) {
                    this.handleError(error);
                }
            },

            // Refresh the books table
            refreshBooksTable: function() {
                // Use Vue's $refs to access the component
                if (this.$refs && this.$refs.booksTable) {
                    this.$refs.booksTable.refresh();
                }
            }
        },

        mounted: function() {
            // Initialize table configuration
            this.initializeBooksTable();

            // Set up New Book button click handler
            const newBookButton = document.getElementById('NewBookButton');
            if (newBookButton) {
                newBookButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.createBook();
                });
            }

            console.log('Books page Vue mixin mounted');
        }
    };

    // Add the mixin to the global array
    addVueMixin(booksPageMixin);

    // Wait for Vue app to be ready
    document.addEventListener('vue-app-ready', function() {
        console.log('Vue app ready - Books page functionality initialized');
    });
})();
