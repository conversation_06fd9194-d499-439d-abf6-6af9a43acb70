﻿using Volo.Abp.DependencyInjection;
using Microsoft.EntityFrameworkCore;

namespace BookStoreVue.Data;

public class BookStoreVueDbSchemaMigrator : ITransientDependency
{
    private readonly IServiceProvider _serviceProvider;

    public BookStoreVueDbSchemaMigrator(
        IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public async Task MigrateAsync()
    {
        
        /* We intentionally resolving the BookStoreVueDbContext
         * from IServiceProvider (instead of directly injecting it)
         * to properly get the connection string of the current tenant in the
         * current scope.
         */

        await _serviceProvider
            .GetRequiredService<BookStoreVueDbContext>()
            .Database
            .MigrateAsync();

    }
}
