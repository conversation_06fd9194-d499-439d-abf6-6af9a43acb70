﻿@page
@using BookStoreVue.Localization
@using BookStoreVue.Permissions
@using BookStoreVue.Pages.Books
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@model IndexModel
@inject IStringLocalizer<BookStoreVueResource> L
@inject IAuthorizationService AuthorizationService
@section scripts
{
    <abp-script src="/js/components/vue-data-table.js" />
    <abp-script src="/Pages/Books/Index.js" />
}
<abp-card>
    <abp-card-header>
        <abp-row>
            <abp-column size-md="_6">
                <abp-card-title>@L["Books"]</abp-card-title>
            </abp-column>
            <abp-column size-md="_6" class="text-end">
                @if (await AuthorizationService.IsGrantedAsync(BookStoreVuePermissions.Books.Create))
                {
                    <abp-button id="NewBookButton"
                                text="@L["NewBook"].Value"
                                icon="plus"
                                button-type="Primary"/>
                }
            </abp-column>
        </abp-row>
    </abp-card-header>
    <abp-card-body>
        <!-- Vue.js DataTable Component -->
        <div id="books-table-container">
            <vue-data-table
                v-if="tableColumns.length > 0"
                :columns="tableColumns"
                ajax-url="/api/app/book"
                :initial-page-size="10"
                :page-size-options="[10, 25, 50, 100]"
                ref="booksTable">
            </vue-data-table>
        </div>

        <!-- Fallback for non-Vue browsers -->
        <noscript>
            <abp-table striped-rows="true" id="BooksTable"></abp-table>
        </noscript>
    </abp-card-body>
</abp-card>