﻿using AutoMapper;
using BookStoreVue.Entities.Books;
using BookStoreVue.Services.Dtos.Books;

namespace BookStoreVue.ObjectMapping;

public class BookStoreVueAutoMapperProfile : Profile
{
    public BookStoreVueAutoMapperProfile()
    {
        CreateMap<Book, BookDto>();
        CreateMap<CreateUpdateBookDto, Book>();
        CreateMap<BookDto, CreateUpdateBookDto>();
        /* Create your AutoMapper object mappings here */
    }
}
