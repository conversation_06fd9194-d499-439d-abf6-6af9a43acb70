/**
 * Vue.js DataTable Component
 * A Vue.js replacement for jQuery DataTables with ABP Framework integration
 */

window.VueDataTable = {
    template: `
        <div class="vue-data-table">
            <!-- Loading overlay -->
            <div v-if="loading" class="d-flex justify-content-center p-4">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
            
            <!-- Table -->
            <div v-else class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th v-for="column in columns" 
                                :key="column.data || column.title"
                                @click="sort(column)"
                                :class="{ 'sortable': column.sortable !== false, 'sorted': sortColumn === (column.data || column.title) }">
                                {{ column.title }}
                                <i v-if="sortColumn === (column.data || column.title)" 
                                   :class="sortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down'"></i>
                                <i v-else-if="column.sortable !== false" class="fas fa-sort text-muted"></i>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="item in items" :key="item.id">
                            <td v-for="column in columns" :key="column.data || column.title">
                                <!-- Actions column -->
                                <div v-if="column.rowAction" class="btn-group">
                                    <button v-for="action in getVisibleActions(column.rowAction.items, item)"
                                            :key="action.text"
                                            @click="executeAction(action, item)"
                                            class="btn btn-sm"
                                            :class="getActionButtonClass(action)">
                                        <i v-if="action.icon" :class="action.icon"></i>
                                        {{ action.text }}
                                    </button>
                                </div>
                                <!-- Regular data column -->
                                <span v-else v-html="renderCell(item, column)"></span>
                            </td>
                        </tr>
                        <tr v-if="items.length === 0">
                            <td :colspan="columns.length" class="text-center text-muted p-4">
                                {{ l('NoDataAvailable') }}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div v-if="totalCount > pageSize" class="d-flex justify-content-between align-items-center mt-3">
                <div class="d-flex align-items-center">
                    <label class="me-2">{{ l('Show') }}:</label>
                    <select v-model="pageSize" @change="loadData" class="form-select form-select-sm" style="width: auto;">
                        <option v-for="size in pageSizeOptions" :key="size" :value="size">{{ size }}</option>
                    </select>
                    <span class="ms-3 text-muted">
                        {{ l('ShowingXToYOfZEntries', getFromRecord(), getToRecord(), totalCount) }}
                    </span>
                </div>
                
                <nav>
                    <ul class="pagination pagination-sm mb-0">
                        <li class="page-item" :class="{ disabled: currentPage === 1 }">
                            <button class="page-link" @click="goToPage(1)" :disabled="currentPage === 1">
                                <i class="fas fa-angle-double-left"></i>
                            </button>
                        </li>
                        <li class="page-item" :class="{ disabled: currentPage === 1 }">
                            <button class="page-link" @click="goToPage(currentPage - 1)" :disabled="currentPage === 1">
                                <i class="fas fa-angle-left"></i>
                            </button>
                        </li>
                        
                        <li v-for="page in getVisiblePages()" 
                            :key="page" 
                            class="page-item" 
                            :class="{ active: page === currentPage }">
                            <button class="page-link" @click="goToPage(page)">{{ page }}</button>
                        </li>
                        
                        <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                            <button class="page-link" @click="goToPage(currentPage + 1)" :disabled="currentPage === totalPages">
                                <i class="fas fa-angle-right"></i>
                            </button>
                        </li>
                        <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                            <button class="page-link" @click="goToPage(totalPages)" :disabled="currentPage === totalPages">
                                <i class="fas fa-angle-double-right"></i>
                            </button>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    `,
    
    props: {
        columns: {
            type: Array,
            required: true
        },
        ajaxUrl: {
            type: String,
            required: true
        },
        initialPageSize: {
            type: Number,
            default: 10
        },
        pageSizeOptions: {
            type: Array,
            default: () => [10, 25, 50, 100]
        }
    },
    
    data() {
        return {
            items: [],
            loading: false,
            currentPage: 1,
            pageSize: this.initialPageSize,
            totalCount: 0,
            sortColumn: null,
            sortDirection: 'asc'
        };
    },
    
    computed: {
        totalPages() {
            return Math.ceil(this.totalCount / this.pageSize);
        }
    },
    
    mounted() {
        this.loadData();
    },
    
    methods: {
        async loadData() {
            this.loading = true;
            try {
                const params = {
                    skipCount: (this.currentPage - 1) * this.pageSize,
                    maxResultCount: this.pageSize
                };
                
                if (this.sortColumn) {
                    params.sorting = `${this.sortColumn} ${this.sortDirection}`;
                }
                
                const response = await this.$http.get(this.ajaxUrl, { params });
                
                if (response.data.items) {
                    this.items = response.data.items;
                    this.totalCount = response.data.totalCount || 0;
                } else if (response.data.result) {
                    this.items = response.data.result.items || [];
                    this.totalCount = response.data.result.totalCount || 0;
                } else {
                    this.items = response.data || [];
                    this.totalCount = this.items.length;
                }
            } catch (error) {
                this.handleError(error);
                this.items = [];
                this.totalCount = 0;
            } finally {
                this.loading = false;
            }
        },
        
        sort(column) {
            if (column.sortable === false) return;
            
            const columnKey = column.data || column.title;
            if (this.sortColumn === columnKey) {
                this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                this.sortColumn = columnKey;
                this.sortDirection = 'asc';
            }
            
            this.currentPage = 1;
            this.loadData();
        },
        
        goToPage(page) {
            if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
                this.currentPage = page;
                this.loadData();
            }
        },
        
        getVisiblePages() {
            const pages = [];
            const maxVisible = 5;
            let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));
            let end = Math.min(this.totalPages, start + maxVisible - 1);
            
            if (end - start + 1 < maxVisible) {
                start = Math.max(1, end - maxVisible + 1);
            }
            
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            
            return pages;
        },
        
        getFromRecord() {
            return this.totalCount === 0 ? 0 : (this.currentPage - 1) * this.pageSize + 1;
        },
        
        getToRecord() {
            return Math.min(this.currentPage * this.pageSize, this.totalCount);
        },
        
        renderCell(item, column) {
            if (column.render && typeof column.render === 'function') {
                return column.render(item[column.data], item);
            }
            
            let value = item[column.data];
            
            if (column.dataFormat === 'datetime' && value) {
                return this.formatDateTime(value);
            }
            
            return value || '';
        },
        
        getVisibleActions(actions, item) {
            return actions.filter(action => {
                if (typeof action.visible === 'function') {
                    return action.visible(item);
                }
                return action.visible !== false;
            });
        },
        
        getActionButtonClass(action) {
            if (action.text === 'Edit') return 'btn-primary';
            if (action.text === 'Delete') return 'btn-danger';
            return 'btn-secondary';
        },
        
        async executeAction(action, item) {
            if (action.confirmMessage) {
                const message = typeof action.confirmMessage === 'function' 
                    ? action.confirmMessage(item) 
                    : action.confirmMessage;
                    
                const confirmed = await this.confirm(message);
                if (!confirmed) return;
            }
            
            if (action.action && typeof action.action === 'function') {
                await action.action({ record: item });
                this.loadData(); // Reload data after action
            }
        },
        
        refresh() {
            this.loadData();
        }
    }
};
